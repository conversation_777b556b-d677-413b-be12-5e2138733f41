import os
import json
import glob
from typing import Any, Dict
from pathlib import Path


class RequestLogger:
    """日志记录器，用于记录请求和响应到编号文件中"""
    
    def __init__(self, logs_dir: str = "logs"):
        self.logs_dir = Path(logs_dir)
        self.logs_dir.mkdir(exist_ok=True)
    
    def _get_next_number(self) -> int:
        """获取下一个序号，基于当前目录中最大的数字"""
        request_files = glob.glob(str(self.logs_dir / "*-request.txt"))
        response_files = glob.glob(str(self.logs_dir / "*-response.txt"))
        
        max_num = 0
        
        # 检查请求文件
        for file_path in request_files:
            filename = os.path.basename(file_path)
            try:
                num = int(filename.split('-')[0])
                max_num = max(max_num, num)
            except (ValueError, IndexError):
                continue
        
        # 检查响应文件
        for file_path in response_files:
            filename = os.path.basename(file_path)
            try:
                num = int(filename.split('-')[0])
                max_num = max(max_num, num)
            except (ValueError, IndexError):
                continue
        
        return max_num + 1
    
    def log_request(self, request_data: Any) -> int:
        """记录请求数据，返回序号"""
        seq_num = self._get_next_number()
        request_file = self.logs_dir / f"{seq_num}-request.txt"
        
        # 将请求数据转换为JSON字符串
        if hasattr(request_data, 'dict'):
            # Pydantic模型
            request_json = json.dumps(request_data.dict(), indent=2, ensure_ascii=False)
        elif isinstance(request_data, dict):
            request_json = json.dumps(request_data, indent=2, ensure_ascii=False)
        else:
            request_json = str(request_data)
        
        with open(request_file, 'w', encoding='utf-8') as f:
            f.write(request_json)
        
        return seq_num
    
    def log_response(self, seq_num: int, response_data: Any):
        """记录响应数据"""
        response_file = self.logs_dir / f"{seq_num}-response.txt"
        
        # 将响应数据转换为JSON字符串
        if isinstance(response_data, dict):
            response_json = json.dumps(response_data, indent=2, ensure_ascii=False)
        else:
            response_json = str(response_data)
        
        with open(response_file, 'w', encoding='utf-8') as f:
            f.write(response_json)
    
    def log_streaming_response(self, seq_num: int, chunks: list):
        """记录流式响应数据"""
        response_file = self.logs_dir / f"{seq_num}-response.txt"
        
        # 将所有chunks合并记录
        response_data = {
            "type": "streaming_response",
            "chunks": chunks,
            "total_chunks": len(chunks)
        }
        
        response_json = json.dumps(response_data, indent=2, ensure_ascii=False)
        
        with open(response_file, 'w', encoding='utf-8') as f:
            f.write(response_json)


# 创建全局实例
request_logger = RequestLogger()
